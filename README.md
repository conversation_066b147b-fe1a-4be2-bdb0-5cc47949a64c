# Time Tracker

一个简单的倒计时应用。

## 阶段 1 目标

### 实现最基本 MVP 功能
    1. 支持两种时间输入方式：时间段（如 1h30m）和截止时间（如 @19:00），支持 # 标签标注任务名称。
    2. 在托盘显示倒计时，并支持开始/暂停/重置/退出菜单（截止时间类型任务除外）。
    3. 支持多任务，每个任务可以设置不同的计时方式。

### 系统托盘设计
```
// 总体布局
      08:00:00     35:00     ⏰

// 截止时间任务
    08:00:00 // 支持左击，打开详情页
    新增
    编辑
    删除
    取消固定

// 计时任务
    08:00:00 // 支持左击，打开详情页
    开始/暂停
    重置
    新增
    编辑
    删除
    取消固定

// 总菜单    // 支持左击，打开详情页
    ⏰
    打开 Time Tracker
    35:00#番茄钟 
               ⍆ 开始 / 暂停
               ⍆ 重置
               ⍆ 编辑
               ⍆ 删除
               ⍆ 取消固定 / 固定
    08:00:00#工作
               ⍆ 编辑
               ⍆ 删除
               ⍆ 取消固定 / 固定
    新增
    删除
    退出
 
```

### 界面设计
```
// 详情页
    08:00:00#工作
    编辑
    删除
// 列表页
    08:00:00#工作
    08:00:00#工作
```

## 阶段 2 目标

计划添加以下功能：
- 完善的用户界面
- 任务日志统计
- 进度条显示
- [其他待规划功能]
