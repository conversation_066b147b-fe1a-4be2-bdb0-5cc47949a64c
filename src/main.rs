#![allow(unused)]

mod task;
mod parser;

use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime, Instant};
use tray_icon::{
    menu::{Menu, MenuEvent, MenuItem, PredefinedMenuItem},
    TrayIcon, TrayIconBuilder, TrayIconEvent, TrayIconEventReceiver,
};
use winit::{
    application::ApplicationHandler,
    event::Event,
    event_loop::{ControlFlow, EventLoop, EventLoopBuilder},
};
use task::{Task, TaskType};
use parser::parse_time_input;

#[derive(Debug)]
enum UserEvent {
    TrayIconEvent(tray_icon::TrayIconEvent),
    MenuEvent(tray_icon::menu::MenuEvent),
    UpdateTimer,
    StartTask(usize),
    PauseTask(usize),
    ResetTask(usize),
    DeleteTask(usize),
}

struct Application {
    tray_icon: Option<TrayIcon>,
    tasks: Arc<Mutex<Vec<Task>>>,
}

impl Application {
    fn new() -> Application {
        Application {
            tray_icon: None,
            tasks: Arc::new(Mutex::new(Vec::new())),
        }
    }

    fn new_tray_icon(tasks: Arc<Mutex<Vec<Task>>>) -> TrayIcon {
        let path = "./assets/icon.jpg";
        let icon = load_icon(std::path::Path::new(path));

        let menu = Self::build_menu(tasks.clone());
        
        TrayIconBuilder::new()
            .with_menu(Box::new(menu))
            .with_tooltip("Time Ticker")
            .with_icon(icon)
            .with_title("⏰")
            .build()
            .unwrap()
    }

    fn build_menu(tasks: Arc<Mutex<Vec<Task>>>) -> Menu {
        let menu = Menu::new();
        
        // 添加任务菜单项
        {
            let tasks = tasks.lock().unwrap();
            for (i, task) in tasks.iter().enumerate() {
                let task_menu = Menu::new();
                
                // 显示剩余时间
                let time_str = format_remaining_time(task.get_remaining_time());
                let time_item = MenuItem::new(&time_str, false, None);
                task_menu.append(&time_item).unwrap();

                // 根据任务类型添加不同的控制选项
                match task.task_type {
                    TaskType::Duration(_) => {
                        let start_pause = MenuItem::new(
                            if task.is_running { "暂停" } else { "开始" },
                            true,
                            None,
                        );
                        task_menu.append(&start_pause).unwrap();

                        let reset = MenuItem::new("重置", true, None);
                        task_menu.append(&reset).unwrap();
                    }
                    TaskType::Deadline(_) => {
                        // 截止时间类型任务不需要开始/暂停/重置
                    }
                }

                // 添加编辑和删除选项
                let edit = MenuItem::new("编辑", true, None);
                task_menu.append(&edit).unwrap();

                let delete = MenuItem::new("删除", true, None);
                task_menu.append(&delete).unwrap();

                // 添加任务到主菜单
                let task_item = MenuItem::new(&format!("{}#{}", time_str, task.name), true, None);
                menu.append(&task_item).unwrap();
            }
        }

        // 添加分隔线
        menu.append(&PredefinedMenuItem::separator()).unwrap();

        // 添加新建任务选项
        let new_task = MenuItem::new("新建任务", true, None);
        menu.append(&new_task).unwrap();

        // 添加退出选项
        let quit = MenuItem::new("退出", true, None);
        menu.append(&quit).unwrap();

        menu
    }

    fn update_tray_icon(&mut self) {
        if let Some(tray_icon) = &self.tray_icon {
            let tasks = self.tasks.lock().unwrap();
            let mut tooltip = String::new();
            
            for task in tasks.iter() {
                let time_str = format_remaining_time(task.get_remaining_time());
                tooltip.push_str(&format!("{}#{}\n", time_str, task.name));
            }

            tray_icon.set_tooltip(Some(&tooltip)).unwrap();
        }
    }

    fn handle_menu_event(&mut self, event: tray_icon::menu::MenuEvent) {
        if let tray_icon::menu::MenuEvent::Click { text, .. } = event {
            if let Some(text) = text {
                if text == "退出" {
                    std::process::exit(0);
                } else if text == "新建任务" {
                    // TODO: 实现新建任务
                } else if text.contains("#") {
                    // 处理任务菜单项点击
                    let tasks = self.tasks.lock().unwrap();
                    if let Some(index) = tasks.iter().position(|t| {
                        let time_str = format_remaining_time(t.get_remaining_time());
                        format!("{}#{}", time_str, t.name) == text
                    }) {
                        // 处理任务操作
                        if text.contains("开始") {
                            if let Ok(mut tasks) = self.tasks.lock() {
                                if let Some(task) = tasks.get_mut(index) {
                                    task.start();
                                }
                            }
                        } else if text.contains("暂停") {
                            if let Ok(mut tasks) = self.tasks.lock() {
                                if let Some(task) = tasks.get_mut(index) {
                                    task.pause();
                                }
                            }
                        } else if text.contains("重置") {
                            if let Ok(mut tasks) = self.tasks.lock() {
                                if let Some(task) = tasks.get_mut(index) {
                                    task.reset();
                                }
                            }
                        } else if text.contains("删除") {
                            if let Ok(mut tasks) = self.tasks.lock() {
                                tasks.remove(index);
                            }
                        }
                    }
                }
            }
        }
    }
}

impl ApplicationHandler<UserEvent> for Application {
    fn resumed(&mut self, _event_loop: &winit::event_loop::ActiveEventLoop) {}

    fn window_event(
        &mut self,
        _event_loop: &winit::event_loop::ActiveEventLoop,
        _window_id: winit::window::WindowId,
        _event: winit::event::WindowEvent,
    ) {
    }

    fn new_events(
        &mut self,
        _event_loop: &winit::event_loop::ActiveEventLoop,
        cause: winit::event::StartCause,
    ) {
        if winit::event::StartCause::Init == cause {
            self.tray_icon = Some(Self::new_tray_icon(self.tasks.clone()));
            
            #[cfg(target_os = "macos")]
            unsafe {
                use objc2_core_foundation::{CFRunLoop};
                let rl = CFRunLoop::main().unwrap();
                CFRunLoop::wake_up(&rl);
            }
        }
    }

    fn user_event(&mut self, event_loop: &winit::event_loop::ActiveEventLoop, event: UserEvent) {
        match event {
            UserEvent::TrayIconEvent(_) => {
                // 处理托盘图标事件
            }
            UserEvent::MenuEvent(event) => {
                self.handle_menu_event(event);
            }
            UserEvent::UpdateTimer => {
                self.update_tray_icon();
                event_loop.set_control_flow(ControlFlow::WaitUntil(
                    Instant::now() + Duration::from_secs(1),
                ));
            }
            UserEvent::StartTask(index) => {
                if let Ok(mut tasks) = self.tasks.lock() {
                    if let Some(task) = tasks.get_mut(index) {
                        task.start();
                    }
                }
            }
            UserEvent::PauseTask(index) => {
                if let Ok(mut tasks) = self.tasks.lock() {
                    if let Some(task) = tasks.get_mut(index) {
                        task.pause();
                    }
                }
            }
            UserEvent::ResetTask(index) => {
                if let Ok(mut tasks) = self.tasks.lock() {
                    if let Some(task) = tasks.get_mut(index) {
                        task.reset();
                    }
                }
            }
            UserEvent::DeleteTask(index) => {
                if let Ok(mut tasks) = self.tasks.lock() {
                    tasks.remove(index);
                }
            }
        }
    }
}

fn format_remaining_time(duration: Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    format!("{:02}:{:02}:{:02}", hours, minutes, seconds)
}

fn main() {
    let event_loop = EventLoop::<UserEvent>::with_user_event().build().unwrap();

    // 设置托盘事件处理器
    let proxy = event_loop.create_proxy();
    TrayIconEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::TrayIconEvent(event));
    }));

    let proxy = event_loop.create_proxy();
    MenuEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::MenuEvent(event));
    }));

    let mut app = Application::new();

    // 设置定时器更新
    let proxy = event_loop.create_proxy();
    std::thread::spawn(move || {
        loop {
            std::thread::sleep(Duration::from_secs(1));
            proxy.send_event(UserEvent::UpdateTimer).unwrap();
        }
    });

    if let Err(err) = event_loop.run_app(&mut app) {
        println!("Error: {:?}", err);
    }
}

fn load_icon(path: &std::path::Path) -> tray_icon::Icon {
    let (icon_rgba, icon_width, icon_height) = {
        let image = image::open(path)
            .expect("Failed to open icon path")
            .into_rgba8();
        let (width, height) = image.dimensions();
        let rgba = image.into_raw();
        (rgba, width, height)
    };
    tray_icon::Icon::from_rgba(icon_rgba, icon_width, icon_height).expect("Failed to open icon")
}