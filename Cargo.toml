[package]
name = "time-ticker"
version = "0.1.0"
edition = "2024"

[dependencies]
image = "0.25.6"
objc2-core-foundation = "0.3.1"
snafu = "0.8.6"
tray-icon = "0.20.1"
winit = "0.30.11"
chrono = "0.4"
regex = "1.10"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# macOS 特定依赖，用于 Dock 控制
[target.'cfg(target_os = "macos")'.dependencies]
objc2 = "0.5"
objc2-app-kit = "0.2"
objc2-foundation = "0.2"
