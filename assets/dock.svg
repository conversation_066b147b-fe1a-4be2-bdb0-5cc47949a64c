<svg width="1024" height="1024" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4285f4"/>
            <stop offset="100%" style="stop-color:#34a853"/>
        </linearGradient>
    </defs>
    
    <!-- 背景 -->
    <rect width="128" height="128" rx="28" fill="url(#bg)"/>
    
    <!-- 同心圆 -->
    <circle cx="64" cy="64" r="42" fill="none" stroke="white" stroke-width="3" opacity="0.4"/>
    <circle cx="64" cy="64" r="30" fill="none" stroke="white" stroke-width="3" opacity="0.6"/>
    <circle cx="64" cy="64" r="18" fill="none" stroke="white" stroke-width="3" opacity="0.8"/>
    <circle cx="64" cy="64" r="7" fill="white"/>
</svg>